# 聚光灯补光技术文档

## 概述

本文档详细说明了在iOS应用中实现聚光灯补光功能的技术原理和实现方法。该系统模拟现实场景中使用专业补光灯拍照的效果，通过计算机视觉和图像处理技术，为人像照片提供高质量的智能补光。

## 系统架构

### 核心组件

1. **SpotLightConfig** - 聚光灯配置结构体
2. **SpotlightEnhancer** - 聚光灯增强处理器
3. **SpotlightVisualizationView** - 聚光灯可视化界面
4. **人物检测算法** - 基于Vision框架的人体分割
5. **法线图生成** - 基于分割遮罩的近似法线计算

### 数据流程

```
原始图像 → 人体分割 → 法线图生成 → 聚光灯计算 → 图像合成 → 最终输出
```

## 聚光灯数学模型

### 1. 光源位置和方向

聚光灯在3D空间中的位置由以下参数定义：

- **位置坐标**: (positionX, positionY, distance)
  - positionX, positionY: 相对于图像的2D位置 (0-1范围)
  - distance: 光源距离系数，影响光照强度和阴影

- **光照方向**: 使用球坐标系定义
  - azimuth: 方位角 (0-360°)
  - elevation: 仰角 (0-90°)

### 2. 聚光灯锥角模型

聚光灯的光照范围由内外锥角定义：

- **innerConeAngle**: 内锥角，光照强度为100%的区域
- **outerConeAngle**: 外锥角，光照强度衰减到0的边界

### 3. 光照衰减函数

#### 角度衰减
```
spotAttenuation = {
    1.0,                                    if angle ≤ innerAngle
    ((outerAngle - angle) / (outerAngle - innerAngle))^falloff,  if innerAngle < angle ≤ outerAngle
    0.0,                                    if angle > outerAngle
}
```

#### 距离衰减
```
distanceAttenuation = 1.0 / (1.0 + distance² × 0.0001)
```

#### 兰伯特光照模型
```
lambertian = max(dot(normal, lightDirection), 0.0)
```

### 4. 最终光照强度计算
```
finalIntensity = ambientLight + intensity × spotAttenuation × distanceAttenuation × lambertian × (1.0 - shadowIntensity)
```

## 技术实现细节

### 1. 人体分割算法

使用Apple Vision框架的VNGeneratePersonSegmentationRequest：

```swift
let segmentationRequest = VNGeneratePersonSegmentationRequest { request, error in
    // 处理分割结果
}
segmentationRequest.qualityLevel = .balanced
```

**优势**：
- 高精度的人体轮廓检测
- 实时处理能力
- 支持多种质量级别

### 2. 法线图生成

基于人体分割遮罩生成近似法线图：

```swift
private static func generateNormalMap(from mask: CIImage, imageSize: CGSize) -> CIImage {
    // 1. 创建高度场
    let heightField = mask.applyingFilter("CIGaussianBlur", parameters: [
        kCIInputRadiusKey: 3.0
    ])
    
    // 2. 转换为法线图
    // 使用CIHeightFieldFromMask或自定义算法
}
```

**原理**：
- 将人体遮罩视为高度场
- 通过梯度计算生成法线向量
- 法线用于计算光照的方向性效果

### 3. 聚光灯光照计算

使用Core Image自定义内核进行GPU加速计算：

```glsl
kernel vec4 spotlightKernel(sampler normalSampler, vec2 lightPos, float lightHeight, vec3 lightDir, 
                           float innerAngle, float outerAngle, float intensity, float falloff, 
                           float shadowIntensity, float ambientLight) {
    // 1. 获取当前像素的法线
    vec3 n = normalize(normal.rgb * 2.0 - 1.0);
    
    // 2. 计算光照向量
    vec3 lightVec = vec3(lightPos.x - coord.x, lightPos.y - coord.y, lightHeight);
    vec3 l = normalize(lightVec);
    
    // 3. 计算聚光灯角度
    float spotAngle = acos(dot(-l, normalize(lightDir)));
    
    // 4. 应用衰减函数
    // ... 详细计算过程
    
    return vec4(finalIntensity, finalIntensity, finalIntensity, 1.0);
}
```

### 4. 图像合成

使用Core Image滤镜链进行图像合成：

1. **色温调整**: CITemperatureAndTint
2. **光照混合**: CIMultiplyCompositing
3. **亮度对比度**: CIColorControls
4. **遮罩混合**: CIBlendWithMask

## 性能优化策略

### 1. GPU加速
- 使用Core Image自定义内核
- 利用Metal性能着色器
- 避免CPU-GPU数据传输

### 2. 内存管理
- 及时释放中间图像
- 使用适当的图像格式
- 控制处理队列的并发数

### 3. 实时预览优化
- 降低预览分辨率
- 使用简化的光照模型
- 异步处理和缓存

## 用户界面设计

### 1. 控制参数
- **光源位置**: 水平/垂直位置滑块
- **光源方向**: 方位角/仰角控制
- **锥角设置**: 内外锥角调节
- **光照属性**: 强度、衰减、阴影等

### 2. 可视化预览
- 实时光锥显示
- 光源位置指示
- 方向箭头标识

### 3. 预设模式
- 顶光、侧光、背光等常用设置
- 一键应用专业补光方案

## 算法优势

### 1. 物理准确性
- 基于真实的光照物理模型
- 考虑距离和角度衰减
- 支持复杂的光影效果

### 2. 高质量输出
- 保持原图细节
- 自然的光影过渡
- 可调节的补光强度

### 3. 用户友好
- 直观的参数控制
- 实时预览效果
- 丰富的预设选项

## 未来改进方向

1. **多光源支持**: 支持多个聚光灯同时工作
2. **材质感知**: 根据皮肤、衣物等不同材质调整光照
3. **AI优化**: 使用机器学习自动优化补光参数
4. **HDR支持**: 支持高动态范围图像处理

## 结论

本聚光灯补光系统通过结合计算机视觉、图形学和图像处理技术，成功实现了专业级的人像补光效果。系统具有高度的可配置性和优秀的性能表现，为用户提供了直观易用的专业补光工具。
