import SwiftUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins
import PhotosUI
import SceneKit
import Metal
import MetalKit

// MARK: - 光照类型定义
enum LightType: String, CaseIterable, Identifiable {
    case directional = "定向光"
    case spot = "聚光灯"
    var id: Self { self }
}

// MARK: - 聚光灯配置
struct SpotLightConfig {
    // 光源位置（相对于图像，0-1范围）
    var positionX: CGFloat = 0.5 // 水平位置
    var positionY: CGFloat = 0.3 // 垂直位置
    var distance: CGFloat = 1.0 // 距离系数（0.1-3.0）

    // 光源方向（球坐标系）
    var azimuth: CGFloat = 0.0 // 方位角（0-360度）
    var elevation: CGFloat = 45.0 // 仰角（0-90度）

    // 聚光灯锥角
    var innerConeAngle: CGFloat = 15.0 // 内锥角（0-90度）
    var outerConeAngle: CGFloat = 30.0 // 外锥角（内锥角-90度）

    // 光照属性
    var intensity: CGFloat = 0.8 // 光源强度（0-2）
    var falloff: CGFloat = 2.0 // 衰减指数（0.5-4.0）
    var colorTemperature: CGFloat = 5500 // 光源色温（2000-10000K）
    var tint: CGFloat = 0.0 // 光源色调偏移（-1到1）

    // 光源颜色
    var lightColorRed: CGFloat = 1.0 // 红色分量（0-1）
    var lightColorGreen: CGFloat = 1.0 // 绿色分量（0-1）
    var lightColorBlue: CGFloat = 1.0 // 蓝色分量（0-1）

    // 阴影和环境光
    var shadowIntensity: CGFloat = 0.3 // 阴影强度（0-1）
    var ambientLight: CGFloat = 0.1 // 环境光强度（0-0.5）
}



// MARK: - 色温配置
struct ColorTemperatureConfig {
    var temperature: CGFloat = 5500 // 色温值（2000-10000K）
    var tint: CGFloat = 0.0 // 色调偏移（-1到1，负值偏绿，正值偏品红）
    var intensity: CGFloat = 0.5 // 色温影响强度（0-1）
}

struct LightConfiguration {
    var type: LightType = .spot // 默认使用聚光灯
    var color: Color = .white
    var intensity: Float = 1.0
    var position: SIMD3<Float> = SIMD3<Float>(0, 1, 1)
    var direction: SIMD3<Float> = SIMD3<Float>(0, -1, -1)
    var spotInnerAngle: Float = 30.0
    var spotOuterAngle: Float = 45.0
}

// MARK: - 主视图
struct LightEnhanceView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState

    @State private var selectedImage: UIImage?
    @State private var originalImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var isProcessing = false
    @State private var showImagePicker = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var beforeAfterPosition: CGFloat = 0.5
    @State private var segmentationMask: VNPixelBufferObservation?
    @State private var isInitialProcessing = false
    @State private var personCenterPoint: CGPoint = CGPoint(x: 0.5, y: 0.5) // 人物中心点（相对坐标）

    // 优化的补光配置（基于HumanLightEnhancement）
    @State private var featherRadius: CGFloat = 25.0

    // 聚光灯配置
    @State private var spotLight = SpotLightConfig()



    // 新增：色温配置
    @State private var colorTemperature = ColorTemperatureConfig()

    // UI控制
    @State private var selectedControlTab: ControlTab = .spotlight

    enum ControlTab: String, CaseIterable, Identifiable {
        case spotlight = "聚光灯"
        case colorTemp = "色温控制"
        case advanced = "高级设置"
        var id: Self { self }
    }

    var body: some View {
        NavigationView {
            ZStack {
                Color.white.ignoresSafeArea()

                VStack(spacing: 0) {
                    headerView

                    if selectedImage == nil {
                        emptyStateView
                    } else {
                        ScrollView(.vertical, showsIndicators: false) {
                            VStack(spacing: 20) {
                                imageDisplayView
                                    .padding(.horizontal, 20)

                                controlPanelView
                                    .padding(.bottom, 100) // 给底部留出空间
                            }
                        }
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
        .onChange(of: selectedImage) { _, newImage in
            if let image = newImage {
                originalImage = image
                performHumanSegmentation()
            }
        }
    }

    // MARK: - 子视图
    private var headerView: some View {
        HStack {
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "arrow.left")
                    .font(.system(size: 18))
                    .foregroundColor(.black)
            }

            Spacer()

            Text("智能补光")
                .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                .foregroundColor(.black)

            Spacer()

            Button(action: {
                showImagePicker = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "photo")
                        .font(.system(size: 16))
                    Text("上传")
                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue)
                .cornerRadius(8)
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .padding(.bottom, 20)
    }

    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()

            Image(systemName: "photo.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.gray.opacity(0.5))

            Text("选择一张人像照片开始补光")
                .font(Font.custom("PingFang SC", size: 18))
                .foregroundColor(.gray)

            Text("AI将自动识别人体并进行智能补光")
                .font(Font.custom("PingFang SC", size: 14))
                .foregroundColor(.gray.opacity(0.7))

            Button(action: {
                showImagePicker = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "person.fill")
                    Text("选择人像照片")
                        .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [Color.blue, Color.blue.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(10)
            }

            Spacer()
        }
    }

    private var imageDisplayView: some View {
        ZStack {
            Color.black.opacity(0.05)
                .cornerRadius(8)

            if let originalImg = originalImage {
                if let processed = processedImage {
                    BeforeAfterSlide(beforeImage: originalImg, afterImage: processed, sliderPosition: $beforeAfterPosition)
                        .cornerRadius(8)
                } else {
                    Image(uiImage: originalImg)
                        .resizable()
                        .scaledToFit()
                        .cornerRadius(8)
                }
            }

            if isProcessing || isInitialProcessing {
                VStack {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text(isInitialProcessing ? "AI分析中..." : "渲染中...")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                        .padding(.top, 10)
                }
            }
        }
        .frame(height: 400) // 固定高度，让图片显示更大
        .clipped()
    }

    private var controlPanelView: some View {
        VStack(spacing: 15) {
            // 控制标签页
            Picker("控制面板", selection: $selectedControlTab) {
                ForEach(ControlTab.allCases) { tab in
                    Text(tab.rawValue)
                }
            }
            .pickerStyle(.segmented)
            .padding(.horizontal, 20)

            // 控制内容
            ZStack {
                spotlightControlsView
                    .opacity(selectedControlTab == .spotlight ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .spotlight)

                colorTemperatureControlsView
                    .opacity(selectedControlTab == .colorTemp ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .colorTemp)

                advancedControlsView
                    .opacity(selectedControlTab == .advanced ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .advanced)
            }
            .disabled(isProcessing)
            .opacity(isProcessing ? 0.5 : 1.0)

            // 保存按钮
            if processedImage != nil {
                Button(action: {
                    saveProcessedImage()
                }) {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                        Text("保存智能补光照片")
                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.green)
                    .cornerRadius(10)
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(15)
        .animation(.easeInOut(duration: 0.2), value: isProcessing)
    }


    // MARK: - 聚光灯控制视图
    private var spotlightControlsView: some View {
        VStack(spacing: 15) {
            VStack(spacing: 10) {
                Text("聚光灯补光")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                Text("模拟专业摄影补光灯的真实光照效果")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }



            // 光源方向控制
            VStack(spacing: 10) {
                Text("光源方向")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)



                EnhancedSlider(title: "仰角", value: $spotLight.elevation, range: 0...90, step: 1, displayMultiplier: 1, unit: "°") {
                    applySpotlightEnhancement()
                }
            }

            // 聚光灯锥角控制
            VStack(spacing: 10) {
                Text("聚光灯锥角")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                EnhancedSlider(title: "内锥角", value: $spotLight.innerConeAngle, range: 5...60, step: 1, displayMultiplier: 1, unit: "°") {
                    // 确保内锥角不大于外锥角
                    if spotLight.innerConeAngle > spotLight.outerConeAngle {
                        spotLight.outerConeAngle = spotLight.innerConeAngle + 5
                    }
                    applySpotlightEnhancement()
                }

                EnhancedSlider(title: "外锥角", value: $spotLight.outerConeAngle, range: 10...90, step: 1, displayMultiplier: 1, unit: "°") {
                    // 确保外锥角不小于内锥角
                    if spotLight.outerConeAngle < spotLight.innerConeAngle {
                        spotLight.innerConeAngle = spotLight.outerConeAngle - 5
                    }
                    applySpotlightEnhancement()
                }
            }

            // 光照属性控制
            VStack(spacing: 10) {
                Text("光照属性")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                EnhancedSlider(title: "光源强度", value: $spotLight.intensity, range: 0...2, displayMultiplier: 100, unit: "%") {
                    applySpotlightEnhancement()
                }

                EnhancedSlider(title: "衰减指数", value: $spotLight.falloff, range: 0.5...4.0, step: 0.1, displayMultiplier: 10, unit: "") {
                    applySpotlightEnhancement()
                }

                EnhancedSlider(title: "阴影强度", value: $spotLight.shadowIntensity, range: 0...1, displayMultiplier: 100, unit: "%") {
                    applySpotlightEnhancement()
                }
            }

            // 光源颜色控制
            VStack(spacing: 10) {
                Text("光源颜色")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                EnhancedSlider(title: "红色", value: $spotLight.lightColorRed, range: 0...1, displayMultiplier: 100, unit: "%") {
                    applySpotlightEnhancement()
                }

                EnhancedSlider(title: "绿色", value: $spotLight.lightColorGreen, range: 0...1, displayMultiplier: 100, unit: "%") {
                    applySpotlightEnhancement()
                }

                EnhancedSlider(title: "蓝色", value: $spotLight.lightColorBlue, range: 0...1, displayMultiplier: 100, unit: "%") {
                    applySpotlightEnhancement()
                }
            }

            // 聚光灯设置
            VStack(spacing: 10) {
                Text("聚光灯设置")
                    .font(.system(size: 14).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                InteractiveSpotlightView(config: $spotLight) {
                    applySpotlightEnhancement()
                }
                .frame(height: 150)
                .padding(.horizontal, 20)
            }
        }
    }



    // MARK: - 色温控制视图
    private var colorTemperatureControlsView: some View {
        VStack(spacing: 15) {
            VStack(spacing: 10) {
                Text("色温控制")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                Text("调节图像的色彩温度和色调平衡")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }

            EnhancedSlider(title: "色温", value: $colorTemperature.temperature, range: 2000...10000, step: 100, displayMultiplier: 1, unit: "K") {
                applyEnhancedLighting()
            }

            EnhancedSlider(title: "色调偏移", value: $colorTemperature.tint, range: -1...1, displayMultiplier: 100, unit: "") {
                applyEnhancedLighting()
            }

            EnhancedSlider(title: "强度", value: $colorTemperature.intensity, range: 0...1, displayMultiplier: 100, unit: "%") {
                applyEnhancedLighting()
            }

            HStack(spacing: 20) {
                VStack(spacing: 5) {
                    Text("暖色调")
                        .font(.system(size: 12))
                        .foregroundColor(.orange)
                    Text("2000K-4000K")
                        .font(.system(size: 10))
                        .foregroundColor(.gray)
                }

                VStack(spacing: 5) {
                    Text("自然光")
                        .font(.system(size: 12))
                        .foregroundColor(.black)
                    Text("5000K-6500K")
                        .font(.system(size: 10))
                        .foregroundColor(.gray)
                }

                VStack(spacing: 5) {
                    Text("冷色调")
                        .font(.system(size: 12))
                        .foregroundColor(.blue)
                    Text("7000K-10000K")
                        .font(.system(size: 10))
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal, 20)

            // 色温快速预设
            VStack(spacing: 10) {
                Text("快速预设")
                    .font(.system(size: 14))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                HStack(spacing: 10) {
                    Button("日光") {
                        colorTemperature.temperature = 5500
                        colorTemperature.tint = 0
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue)
                    .cornerRadius(8)

                    Button("暖光") {
                        colorTemperature.temperature = 3200
                        colorTemperature.tint = 0.1
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.orange)
                    .cornerRadius(8)

                    Button("冷光") {
                        colorTemperature.temperature = 7500
                        colorTemperature.tint = -0.1
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.cyan)
                    .cornerRadius(8)

                    Button("重置") {
                        colorTemperature = ColorTemperatureConfig()
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.black)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
                }
                .padding(.horizontal, 20)
            }
        }
    }

    private var advancedControlsView: some View {
        VStack(spacing: 15) {
            VStack(spacing: 10) {
                Text("高级设置")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)

                Text("精细调节补光效果的参数")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }

            EnhancedSlider(title: "羽化半径", value: $featherRadius, range: 0...100, step: 1, displayMultiplier: 1, unit: "px") {
                applyEnhancedLighting()
            }



            Text("羽化半径控制人物边缘与背景的融合程度，数值越大融合越柔和")
                .font(.system(size: 12))
                .foregroundColor(.gray)
                .padding(.horizontal, 20)
        }
    }

    // MARK: - 逻辑处理

    private func performHumanSegmentation() {
        guard let image = originalImage else { return }

        isInitialProcessing = true

        DispatchQueue.global(qos: .userInitiated).async {
            let segmentationRequest = VNGeneratePersonSegmentationRequest { request, error in
                if let error = error {
                    DispatchQueue.main.async {
                        isInitialProcessing = false
                        alertMessage = "人体分割错误: \(error.localizedDescription)"
                        showAlert = true
                    }
                    return
                }

                guard let observations = request.results as? [VNPixelBufferObservation],
                      let observation = observations.first else {
                    DispatchQueue.main.async {
                        isInitialProcessing = false
                        alertMessage = "未检测到人体，请选择包含人物的照片"
                        showAlert = true
                    }
                    return
                }

                DispatchQueue.main.async {
                    self.segmentationMask = observation
                    // 计算人物中心点
                    self.personCenterPoint = self.calculatePersonCenter(from: observation)
                    isInitialProcessing = false
                    applySpotlightEnhancement()
                }
            }

            segmentationRequest.qualityLevel = .balanced

            guard let cgImage = image.cgImage else {
                DispatchQueue.main.async {
                    isInitialProcessing = false
                    alertMessage = "无法获取图像数据"
                    showAlert = true
                }
                return
            }

            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])

            do {
                try handler.perform([segmentationRequest])
            } catch {
                DispatchQueue.main.async {
                    isInitialProcessing = false
                    alertMessage = "处理图像时出错: \(error.localizedDescription)"
                    showAlert = true
                }
            }
        }
    }

    private func applySpotlightEnhancement() {
        guard let originalImg = originalImage,
              let maskObservation = segmentationMask else { return }

        if isProcessing { return }

        isProcessing = true

        DispatchQueue.global(qos: .userInitiated).async {
            // 使用聚光灯补光算法
            let enhancedImage = SpotlightEnhancer.enhanceImage(
                image: originalImg,
                segmentation: maskObservation,
                spotLight: spotLight,
                featherRadius: featherRadius,
                personCenter: personCenterPoint
            )

            DispatchQueue.main.async {
                self.processedImage = enhancedImage
                self.isProcessing = false
            }
        }
    }

    private func applyEnhancedLighting() {
        guard let originalImg = originalImage,
              let maskObservation = segmentationMask else { return }

        if isProcessing { return }

        isProcessing = true

        DispatchQueue.global(qos: .userInitiated).async {
            // 使用优化的补光算法
            let enhancedImage = OptimizedLightEnhancer.enhanceImage(
                image: originalImg,
                segmentation: maskObservation,
                featherRadius: featherRadius,
                colorTemperature: colorTemperature,
                personCenter: personCenterPoint
            )

            DispatchQueue.main.async {
                self.processedImage = enhancedImage
                self.isProcessing = false
            }
        }
    }

    private func calculatePersonCenter(from segmentation: VNPixelBufferObservation) -> CGPoint {
        // 从人体分割遮罩计算人物的中心点
        let pixelBuffer = segmentation.pixelBuffer
        CVPixelBufferLockBaseAddress(pixelBuffer, .readOnly)
        defer { CVPixelBufferUnlockBaseAddress(pixelBuffer, .readOnly) }

        let width = CVPixelBufferGetWidth(pixelBuffer)
        let height = CVPixelBufferGetHeight(pixelBuffer)
        let bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer)

        guard let baseAddress = CVPixelBufferGetBaseAddress(pixelBuffer) else {
            return CGPoint(x: 0.5, y: 0.5) // 默认中心点
        }

        var totalX: Double = 0
        var totalY: Double = 0
        var pixelCount: Double = 0

        // 遍历像素计算重心
        for y in 0..<height {
            let rowData = baseAddress.advanced(by: y * bytesPerRow)
            for x in 0..<width {
                let pixelValue = rowData.load(fromByteOffset: x, as: UInt8.self)
                let confidence = Double(pixelValue) / 255.0

                if confidence > 0.5 { // 只考虑置信度高的像素
                    totalX += Double(x) * confidence
                    totalY += Double(y) * confidence
                    pixelCount += confidence
                }
            }
        }

        if pixelCount > 0 {
            let centerX = totalX / pixelCount / Double(width)
            let centerY = totalY / pixelCount / Double(height)
            return CGPoint(x: centerX, y: centerY)
        }

        return CGPoint(x: 0.5, y: 0.5) // 默认中心点
    }



    private func saveProcessedImage() {
        guard let image = processedImage else { return }

        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        alertMessage = "智能补光照片已保存到相册"
        showAlert = true
    }
}

// MARK: - 优化的补光增强处理类
class OptimizedLightEnhancer {
    private static var metalDevice: MTLDevice?

    static func enhanceImage(
        image: UIImage,
        segmentation: VNPixelBufferObservation,
        featherRadius: CGFloat,
        colorTemperature: ColorTemperatureConfig,
        personCenter: CGPoint
    ) -> UIImage {
        guard let cgImage = image.cgImage else {
            return image
        }

        let ciImage = CIImage(cgImage: cgImage)
        let context = CIContext(options: [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .outputColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])

        // 1. 使用原始图像作为基础
        let baseImage = ciImage

        // 2. 创建并处理人体遮罩
        var maskCIImage = CIImage(cvPixelBuffer: segmentation.pixelBuffer)

        // 3. 缩放遮罩到与原图相同大小
        let imageSize = baseImage.extent.size
        let maskSize = maskCIImage.extent.size
        let scaleX = imageSize.width / maskSize.width
        let scaleY = imageSize.height / maskSize.height
        let scaleTransform = CGAffineTransform(scaleX: scaleX, y: scaleY)
        maskCIImage = maskCIImage.transformed(by: scaleTransform)

        // 4. 优化的羽化处理
        let featheredMask = applyOptimizedFeathering(to: maskCIImage, radius: featherRadius)

        // 5. 应用方向性光源效果
        let lightEnhancedImage = applyDirectionalLighting(
            to: baseImage,
            mask: featheredMask,
            lightConfig: directionalLight,
            personCenter: personCenter,
            imageSize: imageSize
        )

        // 6. 应用色温调整
        let colorTempAdjustedImage = applyColorTemperature(
            to: lightEnhancedImage,
            config: colorTemperature
        )

        // 7. 最终处理和输出
        guard let outputCGImage = context.createCGImage(colorTempAdjustedImage, from: colorTempAdjustedImage.extent) else {
            return image
        }

        return UIImage(cgImage: outputCGImage, scale: image.scale, orientation: image.imageOrientation)
    }



    // 优化的羽化处理
    private static func applyOptimizedFeathering(to mask: CIImage, radius: CGFloat) -> CIImage {
        // 使用双重模糊来获得更自然的羽化效果
        let blur1 = mask.applyingFilter("CIGaussianBlur", parameters: [
            kCIInputRadiusKey: radius * 0.5
        ])

        let blur2 = blur1.applyingFilter("CIGaussianBlur", parameters: [
            kCIInputRadiusKey: radius * 0.3
        ])

        // 混合两次模糊的结果
        return blur2.applyingFilter("CIAdditionCompositing", parameters: [
            kCIInputBackgroundImageKey: blur1
        ]).cropped(to: mask.extent)
    }



    // MARK: - 方向性光源处理
    private static func applyDirectionalLighting(
        to image: CIImage,
        mask: CIImage,
        lightConfig: DirectionalLightConfig,
        personCenter: CGPoint,
        imageSize: CGSize
    ) -> CIImage {
        guard lightConfig.intensity > 0 else { return image }

        // 创建方向性光照遮罩，只影响人体区域
        let lightMask = createDirectionalLightMask(
            size: imageSize,
            lightConfig: lightConfig,
            personCenter: personCenter,
            humanMask: mask
        )

        // 1. 先应用光源色温
        var workingImage = image
        if lightConfig.colorTemperature != 5500 || lightConfig.tint != 0 {
            workingImage = applyLightColorTemperature(to: image, lightConfig: lightConfig, mask: lightMask)
        }

        // 2. 创建亮度增强图像
        guard let brightnessFilter = CIFilter(name: "CIColorControls") else {
            return workingImage
        }

        brightnessFilter.setValue(workingImage, forKey: kCIInputImageKey)
        brightnessFilter.setValue(NSNumber(value: 1.0 + lightConfig.intensity * 0.5), forKey: kCIInputBrightnessKey)
        brightnessFilter.setValue(NSNumber(value: 1.0 + lightConfig.intensity * 0.3), forKey: kCIInputContrastKey)

        guard let brightenedImage = brightnessFilter.outputImage else { return workingImage }

        // 使用遮罩混合原图和增强图像
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            return image
        }

        blendFilter.setValue(brightenedImage, forKey: kCIInputImageKey)
        blendFilter.setValue(image, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(lightMask, forKey: kCIInputMaskImageKey)

        return blendFilter.outputImage ?? image
    }

    // 创建方向性光照遮罩
    private static func createDirectionalLightMask(
        size: CGSize,
        lightConfig: DirectionalLightConfig,
        personCenter: CGPoint,
        humanMask: CIImage
    ) -> CIImage {
        // 计算光源方向向量（修正坐标系）
        let angleRad = (lightConfig.angle - 90) * .pi / 180.0  // 修正角度，0度为上方
        let elevationFactor = lightConfig.elevation / 90.0

        // 计算光源位置（相对于人物中心）
        let lightDistance = max(size.width, size.height) * 0.8
        let lightX = personCenter.x * size.width + cos(angleRad) * lightDistance * elevationFactor
        let lightY = personCenter.y * size.height + sin(angleRad) * lightDistance * elevationFactor

        // 创建径向渐变作为光照基础
        guard let radialGradient = CIFilter(name: "CIRadialGradient") else {
            return humanMask
        }

        radialGradient.setValue(CIVector(x: lightX, y: lightY), forKey: "inputCenter")
        radialGradient.setValue(NSNumber(value: 0), forKey: "inputRadius0")
        radialGradient.setValue(NSNumber(value: lightDistance * (0.5 + lightConfig.softness)), forKey: "inputRadius1")
        radialGradient.setValue(CIColor(red: lightConfig.intensity, green: lightConfig.intensity, blue: lightConfig.intensity, alpha: 1.0), forKey: "inputColor0")
        radialGradient.setValue(CIColor(red: 0, green: 0, blue: 0, alpha: 1.0), forKey: "inputColor1")

        guard let gradientImage = radialGradient.outputImage else {
            return humanMask
        }

        let croppedGradient = gradientImage.cropped(to: CGRect(origin: .zero, size: size))

        // 将光照渐变与人体遮罩相乘，确保只影响人体区域
        guard let multiplyFilter = CIFilter(name: "CIMultiplyCompositing") else {
            return humanMask
        }

        multiplyFilter.setValue(croppedGradient, forKey: kCIInputImageKey)
        multiplyFilter.setValue(humanMask, forKey: kCIInputBackgroundImageKey)

        return multiplyFilter.outputImage ?? humanMask
    }

    // 应用光源色温（只影响光照区域）
    private static func applyLightColorTemperature(
        to image: CIImage,
        lightConfig: DirectionalLightConfig,
        mask: CIImage
    ) -> CIImage {
        // 创建色温调整
        guard let tempFilter = CIFilter(name: "CITemperatureAndTint") else {
            return image
        }

        tempFilter.setValue(image, forKey: kCIInputImageKey)

        // 将色温值转换为CIFilter期望的范围（与全局色温保持一致）
        let tempValue = (lightConfig.colorTemperature - 6500) / 3500.0  // 归一化到-1到1范围
        let tintValue = lightConfig.tint
        let tempVector = CIVector(x: tempValue, y: tintValue)
        tempFilter.setValue(tempVector, forKey: "inputNeutral")
        tempFilter.setValue(CIVector(x: 0, y: 0), forKey: "inputTargetNeutral")

        guard let tempAdjustedImage = tempFilter.outputImage else { return image }

        // 使用光照遮罩混合
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            return image
        }

        blendFilter.setValue(tempAdjustedImage, forKey: kCIInputImageKey)
        blendFilter.setValue(image, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(mask, forKey: kCIInputMaskImageKey)

        return blendFilter.outputImage ?? image
    }

    // MARK: - 色温处理
    private static func applyColorTemperature(
        to image: CIImage,
        config: ColorTemperatureConfig
    ) -> CIImage {
        guard config.intensity > 0 else { return image }

        // 1. 色温调整
        guard let tempFilter = CIFilter(name: "CITemperatureAndTint") else {
            return image
        }
        tempFilter.setValue(image, forKey: kCIInputImageKey)

        // 使用更简单有效的色温调整方式
        // CITemperatureAndTint的参数范围：温度-1到1，色调-1到1
        let tempValue = (config.temperature - 6500) / 3500.0  // 归一化到-1到1范围
        let tintValue = config.tint

        let tempVector = CIVector(x: tempValue, y: tintValue)
        tempFilter.setValue(tempVector, forKey: "inputNeutral")
        tempFilter.setValue(CIVector(x: 0, y: 0), forKey: "inputTargetNeutral")

        guard let tempAdjustedImage = tempFilter.outputImage else { return image }

        // 2. 强度混合 - 使用更简单的混合方式
        if config.intensity >= 1.0 {
            return tempAdjustedImage
        }

        // 创建权重遮罩来控制混合强度
        let weightColor = CIColor(red: config.intensity, green: config.intensity, blue: config.intensity, alpha: 1.0)
        let weightImage = CIImage(color: weightColor).cropped(to: image.extent)

        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            return tempAdjustedImage
        }

        blendFilter.setValue(tempAdjustedImage, forKey: kCIInputImageKey)
        blendFilter.setValue(image, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(weightImage, forKey: kCIInputMaskImageKey)

        return blendFilter.outputImage ?? image
    }

}

// MARK: - 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// MARK: - 增强的滑块控件
struct EnhancedSlider: View {
    let title: String
    @Binding var value: CGFloat
    let range: ClosedRange<CGFloat>
    var step: CGFloat = 0.01
    var displayMultiplier: CGFloat = 1.0
    var unit: String = ""
    let onEditingChanged: () -> Void

    var body: some View {
        HStack {
            Text(title)
                .font(Font.custom("PingFang SC", size: 16))
                .foregroundColor(.black)
                .frame(width: 80, alignment: .leading)

            GeometryReader { geo in
                let width = geo.size.width
                let percentage = (value - range.lowerBound) / (range.upperBound - range.lowerBound)
                let knobPosition = width * percentage

                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 4)
                        .cornerRadius(2)

                    Rectangle()
                        .fill(LinearGradient(
                            colors: [Color.orange, Color.yellow],
                            startPoint: .leading,
                            endPoint: .trailing
                        ))
                        .frame(width: knobPosition, height: 4)
                        .cornerRadius(2)

                    Image(systemName: "circle.fill")
                        .resizable()
                        .frame(width: 20, height: 20)
                        .foregroundColor(.white)
                        .shadow(radius: 3)
                        .overlay(Circle().stroke(Color.orange.opacity(0.3), lineWidth: 1))
                        .position(x: knobPosition, y: geo.size.height / 2)
                        .gesture(
                            DragGesture()
                                .onChanged { gestureValue in
                                    let newPercentage = max(0, min(1, gestureValue.location.x / width))
                                    let newValue = range.lowerBound + newPercentage * (range.upperBound - range.lowerBound)
                                    self.value = newValue
                                }
                                .onEnded { _ in
                                    onEditingChanged()
                                }
                        )
                }
            }
            .frame(height: 30)

            Text("\(Int(value * displayMultiplier))\(unit)")
                .font(.system(size: 14))
                .foregroundColor(.gray)
                .frame(width: 50, alignment: .trailing)
        }
        .padding(.horizontal, 20)
    }
}



// MARK: - 前后对比滑块控件
struct BeforeAfterSlide: View {
    let beforeImage: UIImage
    let afterImage: UIImage
    @Binding var sliderPosition: CGFloat

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 处理后的图像（底层）
                Image(uiImage: afterImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: geometry.size.width, height: geometry.size.height)

                // 原图（上层，被裁剪）
                Image(uiImage: beforeImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .clipShape(
                        Rectangle()
                            .path(in: CGRect(x: 0, y: 0, width: geometry.size.width * sliderPosition, height: geometry.size.height))
                    )

                // 分割线
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 2, height: geometry.size.height)
                    .position(x: geometry.size.width * sliderPosition, y: geometry.size.height / 2)
                    .shadow(color: Color.black.opacity(0.5), radius: 2, x: 0, y: 0)

                // 滑块控制柄
                Circle()
                    .fill(Color.white)
                    .frame(width: 30, height: 30)
                    .shadow(color: Color.black.opacity(0.5), radius: 2, x: 0, y: 0)
                    .overlay(
                        HStack(spacing: 3) {
                            Image(systemName: "arrow.left")
                                .font(.system(size: 8))
                            Image(systemName: "arrow.right")
                                .font(.system(size: 8))
                        }
                        .foregroundColor(.black)
                    )
                    .position(x: geometry.size.width * sliderPosition, y: 30)
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                let newPosition = value.location.x / geometry.size.width
                                sliderPosition = min(1, max(0, newPosition))
                            }
                    )
            }
        }
    }
}

// MARK: - 交互式聚光灯设置视图
struct InteractiveSpotlightView: View {
    @Binding var config: SpotLightConfig
    let onChange: () -> Void

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                Rectangle()
                    .fill(Color.black.opacity(0.1))
                    .cornerRadius(8)

                // 人物位置（中心）
                let personX = geometry.size.width * 0.5
                let personY = geometry.size.height * 0.6

                Circle()
                    .fill(Color.blue)
                    .frame(width: 12, height: 12)
                    .position(x: personX, y: personY)

                // 聚光灯位置（可拖拽）
                let lightX = geometry.size.width * config.positionX
                let lightY = geometry.size.height * config.positionY

                Circle()
                    .fill(Color.yellow)
                    .frame(width: 16, height: 16)
                    .position(x: lightX, y: lightY)
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                let newX = max(0, min(1, value.location.x / geometry.size.width))
                                let newY = max(0, min(1, value.location.y / geometry.size.height))
                                config.positionX = newX
                                config.positionY = newY
                                onChange()
                            }
                    )

                // 聚光灯光锥
                SpotlightConeShape(
                    lightPosition: CGPoint(x: lightX, y: lightY),
                    targetPosition: CGPoint(x: personX, y: personY),
                    innerAngle: config.innerConeAngle,
                    outerAngle: config.outerConeAngle,
                    distance: 80
                )
                .fill(
                    RadialGradient(
                        colors: [
                            Color(red: config.lightColorRed, green: config.lightColorGreen, blue: config.lightColorBlue).opacity(0.4),
                            Color(red: config.lightColorRed, green: config.lightColorGreen, blue: config.lightColorBlue).opacity(0.2),
                            Color(red: config.lightColorRed, green: config.lightColorGreen, blue: config.lightColorBlue).opacity(0.1),
                            Color.clear
                        ],
                        center: UnitPoint(x: lightX / geometry.size.width, y: lightY / geometry.size.height),
                        startRadius: 5,
                        endRadius: 80
                    )
                )

                // 方向指示器（可拖拽箭头）
                let azimuthRad = config.azimuth * .pi / 180.0
                let directionLength: CGFloat = 35
                let directionEndX = lightX + cos(azimuthRad - .pi/2) * directionLength
                let directionEndY = lightY + sin(azimuthRad - .pi/2) * directionLength

                Path { path in
                    path.move(to: CGPoint(x: lightX, y: lightY))
                    path.addLine(to: CGPoint(x: directionEndX, y: directionEndY))
                }
                .stroke(Color.red, lineWidth: 2)

                // 箭头（可拖拽）
                Image(systemName: "arrow.up")
                    .font(.system(size: 14))
                    .foregroundColor(.red)
                    .rotationEffect(.degrees(config.azimuth))
                    .position(x: directionEndX, y: directionEndY)
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                let dx = value.location.x - lightX
                                let dy = value.location.y - lightY
                                let angle = atan2(dy, dx) * 180.0 / .pi + 90
                                config.azimuth = angle < 0 ? angle + 360 : angle
                                onChange()
                            }
                    )

                // 标签
                VStack(alignment: .leading, spacing: 2) {
                    Text("蓝点: 人物")
                        .font(.system(size: 10))
                        .foregroundColor(.blue)
                    Text("黄点: 聚光灯（可拖拽）")
                        .font(.system(size: 10))
                        .foregroundColor(.orange)
                    Text("红箭头: 光照方向（可拖拽）")
                        .font(.system(size: 10))
                        .foregroundColor(.red)
                }
                .padding(8)
                .background(Color.white.opacity(0.8))
                .cornerRadius(6)
                .position(x: geometry.size.width * 0.2, y: geometry.size.height * 0.15)
            }
        }
    }
}

// MARK: - 聚光灯光锥形状
struct SpotlightConeShape: Shape {
    let lightPosition: CGPoint
    let targetPosition: CGPoint
    let innerAngle: CGFloat
    let outerAngle: CGFloat
    let distance: CGFloat

    func path(in rect: CGRect) -> Path {
        var path = Path()

        // 计算光照方向
        let dx = targetPosition.x - lightPosition.x
        let dy = targetPosition.y - lightPosition.y
        let angle = atan2(dy, dx)

        // 外锥角的两条边
        let outerAngleRad = outerAngle * .pi / 180.0
        let leftAngle = angle - outerAngleRad / 2
        let rightAngle = angle + outerAngleRad / 2

        // 计算光锥的端点
        let leftEndX = lightPosition.x + cos(leftAngle) * distance
        let leftEndY = lightPosition.y + sin(leftAngle) * distance
        let rightEndX = lightPosition.x + cos(rightAngle) * distance
        let rightEndY = lightPosition.y + sin(rightAngle) * distance

        // 绘制光锥
        path.move(to: lightPosition)
        path.addLine(to: CGPoint(x: leftEndX, y: leftEndY))
        path.addArc(
            center: lightPosition,
            radius: distance,
            startAngle: Angle(radians: leftAngle),
            endAngle: Angle(radians: rightAngle),
            clockwise: false
        )
        path.addLine(to: lightPosition)

        return path
    }
}

// MARK: - 聚光灯增强器
class SpotlightEnhancer {

    static func enhanceImage(
        image: UIImage,
        segmentation: VNPixelBufferObservation,
        spotLight: SpotLightConfig,
        featherRadius: CGFloat,
        personCenter: CGPoint
    ) -> UIImage {
        guard let cgImage = image.cgImage else {
            return image
        }

        let ciImage = CIImage(cgImage: cgImage)
        let context = CIContext(options: [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .outputColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])

        // 1. 创建并处理人体遮罩
        var maskCIImage = CIImage(cvPixelBuffer: segmentation.pixelBuffer)

        // 2. 缩放遮罩到与原图相同大小
        let imageSize = ciImage.extent.size
        let maskSize = maskCIImage.extent.size
        let scaleX = imageSize.width / maskSize.width
        let scaleY = imageSize.height / maskSize.height
        let scaleTransform = CGAffineTransform(scaleX: scaleX, y: scaleY)
        maskCIImage = maskCIImage.transformed(by: scaleTransform)

        // 3. 羽化处理
        let featheredMask = applyFeathering(to: maskCIImage, radius: featherRadius)

        // 4. 生成法线图
        let normalMap = generateNormalMap(from: featheredMask, imageSize: imageSize)

        // 5. 应用聚光灯照明
        let lightEnhancedImage = applySpotlightLighting(
            to: ciImage,
            mask: featheredMask,
            normalMap: normalMap,
            spotLight: spotLight,
            personCenter: personCenter,
            imageSize: imageSize
        )

        // 6. 最终处理和输出
        guard let outputCGImage = context.createCGImage(lightEnhancedImage, from: lightEnhancedImage.extent) else {
            return image
        }

        return UIImage(cgImage: outputCGImage, scale: image.scale, orientation: image.imageOrientation)
    }

    // 羽化处理
    private static func applyFeathering(to mask: CIImage, radius: CGFloat) -> CIImage {
        let blur = mask.applyingFilter("CIGaussianBlur", parameters: [
            kCIInputRadiusKey: radius * 0.5
        ])
        return blur.cropped(to: mask.extent)
    }

    // 生成法线图
    private static func generateNormalMap(from mask: CIImage, imageSize: CGSize) -> CIImage {
        // 使用高度场生成法线图
        // 首先对遮罩进行轻微模糊以创建高度场
        let heightField = mask.applyingFilter("CIGaussianBlur", parameters: [
            kCIInputRadiusKey: 3.0
        ])

        // 使用CIHeightFieldFromMask生成高度场，然后转换为法线图
        guard let heightToNormalFilter = CIFilter(name: "CIHeightFieldFromMask") else {
            // 如果没有该滤镜，创建简单的法线图
            return createSimpleNormalMap(from: mask, imageSize: imageSize)
        }

        heightToNormalFilter.setValue(heightField, forKey: kCIInputImageKey)
        heightToNormalFilter.setValue(NSNumber(value: 10.0), forKey: "inputRadius")

        guard let normalMapImage = heightToNormalFilter.outputImage else {
            return createSimpleNormalMap(from: mask, imageSize: imageSize)
        }

        return normalMapImage.cropped(to: CGRect(origin: .zero, size: imageSize))
    }

    // 创建简单法线图
    private static func createSimpleNormalMap(from mask: CIImage, imageSize: CGSize) -> CIImage {
        // 创建一个基础的法线图（指向观察者）
        let normalColor = CIColor(red: 0.5, green: 0.5, blue: 1.0, alpha: 1.0) // 法线指向Z轴正方向
        let normalImage = CIImage(color: normalColor).cropped(to: CGRect(origin: .zero, size: imageSize))

        // 使用遮罩混合，只在人体区域应用法线
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            return normalImage
        }

        let defaultNormal = CIImage(color: CIColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0))
            .cropped(to: CGRect(origin: .zero, size: imageSize))

        blendFilter.setValue(normalImage, forKey: kCIInputImageKey)
        blendFilter.setValue(defaultNormal, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(mask, forKey: kCIInputMaskImageKey)

        return blendFilter.outputImage ?? normalImage
    }

    // 应用聚光灯照明
    private static func applySpotlightLighting(
        to image: CIImage,
        mask: CIImage,
        normalMap: CIImage,
        spotLight: SpotLightConfig,
        personCenter: CGPoint,
        imageSize: CGSize
    ) -> CIImage {
        guard spotLight.intensity > 0 else { return image }

        // 1. 计算聚光灯位置（3D空间）
        let lightPosX = spotLight.positionX * imageSize.width
        let lightPosY = spotLight.positionY * imageSize.height
        let lightPosZ = spotLight.distance * max(imageSize.width, imageSize.height) * 0.5

        // 2. 计算光照方向向量
        let azimuthRad = spotLight.azimuth * .pi / 180.0
        let elevationRad = spotLight.elevation * .pi / 180.0

        let lightDirX = cos(elevationRad) * sin(azimuthRad)
        let lightDirY = cos(elevationRad) * cos(azimuthRad)
        let lightDirZ = -sin(elevationRad)

        // 3. 创建聚光灯光照图
        let lightingMap = createSpotlightMap(
            imageSize: imageSize,
            lightPosition: CGPoint(x: lightPosX, y: lightPosY),
            lightHeight: lightPosZ,
            lightDirection: (x: lightDirX, y: lightDirY, z: lightDirZ),
            spotLight: spotLight,
            normalMap: normalMap
        )

        // 4. 应用色温调整
        var colorAdjustedImage = image
        if spotLight.colorTemperature != 5500 || spotLight.tint != 0 {
            colorAdjustedImage = applySpotlightColorTemperature(to: image, spotLight: spotLight)
        }

        // 5. 创建光照增强图像
        let enhancedImage = createLightEnhancedImage(
            baseImage: colorAdjustedImage,
            lightingMap: lightingMap,
            spotLight: spotLight
        )

        // 6. 使用人体遮罩混合
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            return image
        }

        blendFilter.setValue(enhancedImage, forKey: kCIInputImageKey)
        blendFilter.setValue(image, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(mask, forKey: kCIInputMaskImageKey)

        return blendFilter.outputImage ?? image
    }

    // 创建聚光灯光照图
    private static func createSpotlightMap(
        imageSize: CGSize,
        lightPosition: CGPoint,
        lightHeight: CGFloat,
        lightDirection: (x: CGFloat, y: CGFloat, z: CGFloat),
        spotLight: SpotLightConfig,
        normalMap: CIImage
    ) -> CIImage {
        // 创建自定义核心图像滤镜来计算聚光灯照明
        let kernel = CIKernel(source: """
            kernel vec4 spotlightKernel(sampler normalSampler, vec2 lightPos, float lightHeight, vec3 lightDir,
                                       float innerAngle, float outerAngle, float intensity, float falloff,
                                       float shadowIntensity, vec3 lightColor) {
                vec2 coord = destCoord();
                vec4 normal = sample(normalSampler, samplerCoord(normalSampler));

                // 将法线从[0,1]范围转换到[-1,1]范围
                vec3 n = normalize(normal.rgb * 2.0 - 1.0);

                // 计算从像素到光源的向量
                vec3 lightVec = vec3(lightPos.x - coord.x, lightPos.y - coord.y, lightHeight);
                float distance = length(lightVec);
                vec3 l = normalize(lightVec);

                // 计算光照方向与光源到像素方向的夹角
                float spotAngle = acos(dot(-l, normalize(lightDir)));
                float spotAngleDeg = degrees(spotAngle);

                // 聚光灯衰减计算
                float spotAttenuation = 1.0;
                if (spotAngleDeg > outerAngle) {
                    spotAttenuation = 0.0;
                } else if (spotAngleDeg > innerAngle) {
                    float factor = (outerAngle - spotAngleDeg) / (outerAngle - innerAngle);
                    spotAttenuation = pow(factor, falloff);
                }

                // 距离衰减
                float distanceAttenuation = 1.0 / (1.0 + distance * distance * 0.0001);

                // 兰伯特光照模型
                float lambertian = max(dot(n, l), 0.0);

                // 最终光照强度 - 限制最大值避免过度曝光
                float lightIntensity = intensity * spotAttenuation * distanceAttenuation * lambertian * 0.5;

                // 添加基础环境光和阴影，并限制在合理范围内
                float baseAmbient = 0.3; // 固定的基础环境光
                float finalIntensity = clamp(baseAmbient + lightIntensity * (1.0 - shadowIntensity), 0.2, 2.0);

                // 应用光源颜色
                vec3 finalColor = lightColor * finalIntensity;
                finalColor = clamp(finalColor, 0.0, 2.0);

                return vec4(finalColor, 1.0);
            }
        """)

        guard let spotlightKernel = kernel else {
            // 如果自定义核心失败，使用简化版本
            return createSimplifiedSpotlightMap(
                imageSize: imageSize,
                lightPosition: lightPosition,
                lightHeight: lightHeight,
                spotLight: spotLight
            )
        }

        let arguments = [
            normalMap,
            CIVector(x: lightPosition.x, y: lightPosition.y),
            NSNumber(value: Float(lightHeight)),
            CIVector(x: lightDirection.x, y: lightDirection.y, z: lightDirection.z),
            NSNumber(value: Float(spotLight.innerConeAngle)),
            NSNumber(value: Float(spotLight.outerConeAngle)),
            NSNumber(value: Float(spotLight.intensity)),
            NSNumber(value: Float(spotLight.falloff)),
            NSNumber(value: Float(spotLight.shadowIntensity)),
            CIVector(x: spotLight.lightColorRed, y: spotLight.lightColorGreen, z: spotLight.lightColorBlue)
        ]

        let extent = CGRect(origin: .zero, size: imageSize)
        return spotlightKernel.apply(extent: extent, roiCallback: { _, rect in
            return rect
        }, arguments: arguments) ??
               createSimplifiedSpotlightMap(imageSize: imageSize, lightPosition: lightPosition, lightHeight: lightHeight, spotLight: spotLight)
    }
    // 创建简化的聚光灯光照图（备用方案）
    private static func createSimplifiedSpotlightMap(
        imageSize: CGSize,
        lightPosition: CGPoint,
        lightHeight: CGFloat,
        spotLight: SpotLightConfig
    ) -> CIImage {
        // 使用径向渐变模拟聚光灯效果
        guard let radialGradient = CIFilter(name: "CIRadialGradient") else {
            let uniformColor = CIColor(red: spotLight.intensity, green: spotLight.intensity, blue: spotLight.intensity, alpha: 1.0)
            return CIImage(color: uniformColor).cropped(to: CGRect(origin: .zero, size: imageSize))
        }

        // 计算聚光灯影响范围
        let maxDistance = max(imageSize.width, imageSize.height)
        let innerRadius = maxDistance * spotLight.innerConeAngle / 180.0 * 0.5
        let outerRadius = maxDistance * spotLight.outerConeAngle / 180.0 * 0.5

        radialGradient.setValue(CIVector(x: lightPosition.x, y: lightPosition.y), forKey: "inputCenter")
        radialGradient.setValue(NSNumber(value: Float(innerRadius)), forKey: "inputRadius0")
        radialGradient.setValue(NSNumber(value: Float(outerRadius)), forKey: "inputRadius1")

        // 应用光源颜色和强度
        let centerColor = CIColor(
            red: spotLight.lightColorRed * spotLight.intensity,
            green: spotLight.lightColorGreen * spotLight.intensity,
            blue: spotLight.lightColorBlue * spotLight.intensity,
            alpha: 1.0
        )
        let edgeColor = CIColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0) // 基础环境光

        radialGradient.setValue(centerColor, forKey: "inputColor0")
        radialGradient.setValue(edgeColor, forKey: "inputColor1")

        guard let gradientImage = radialGradient.outputImage else {
            let uniformColor = CIColor(red: spotLight.intensity, green: spotLight.intensity, blue: spotLight.intensity, alpha: 1.0)
            return CIImage(color: uniformColor).cropped(to: CGRect(origin: .zero, size: imageSize))
        }

        return gradientImage.cropped(to: CGRect(origin: .zero, size: imageSize))
    }

    // 应用聚光灯色温
    private static func applySpotlightColorTemperature(to image: CIImage, spotLight: SpotLightConfig) -> CIImage {
        guard let tempFilter = CIFilter(name: "CITemperatureAndTint") else {
            return image
        }

        tempFilter.setValue(image, forKey: kCIInputImageKey)

        let tempValue = (spotLight.colorTemperature - 6500) / 3500.0
        let tintValue = spotLight.tint
        let tempVector = CIVector(x: tempValue, y: tintValue)
        tempFilter.setValue(tempVector, forKey: "inputNeutral")
        tempFilter.setValue(CIVector(x: 0, y: 0), forKey: "inputTargetNeutral")

        return tempFilter.outputImage ?? image
    }

    // 创建光照增强图像
    private static func createLightEnhancedImage(
        baseImage: CIImage,
        lightingMap: CIImage,
        spotLight: SpotLightConfig
    ) -> CIImage {
        // 使用乘法混合模式应用光照
        guard let multiplyFilter = CIFilter(name: "CIMultiplyCompositing") else {
            return baseImage
        }

        multiplyFilter.setValue(baseImage, forKey: kCIInputImageKey)
        multiplyFilter.setValue(lightingMap, forKey: kCIInputBackgroundImageKey)

        guard let multipliedImage = multiplyFilter.outputImage else {
            return baseImage
        }

        // 使用柔光混合模式，创造自然的光照效果
        guard let softLightFilter = CIFilter(name: "CISoftLightBlendMode") else {
            // 备用方案：使用屏幕混合模式
            guard let screenFilter = CIFilter(name: "CIScreenBlendMode") else {
                return baseImage
            }
            screenFilter.setValue(baseImage, forKey: kCIInputImageKey)
            screenFilter.setValue(lightingMap, forKey: kCIInputBackgroundImageKey)
            return screenFilter.outputImage ?? baseImage
        }

        softLightFilter.setValue(baseImage, forKey: kCIInputImageKey)
        softLightFilter.setValue(lightingMap, forKey: kCIInputBackgroundImageKey)

        return softLightFilter.outputImage ?? baseImage
    }
}
