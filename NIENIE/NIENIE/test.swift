import SwiftUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins
import PhotosUI
import SceneKit
import Metal
import MetalKit

// MARK: - 光照类型定义
enum LightType: String, CaseIterable, Identifiable {
    case directional = "定向光"
    case spot = "聚光灯"
    var id: Self { self }
}

// MARK: - 方向性光源配置
struct DirectionalLightConfig {
    var angle: CGFloat = 45.0 // 光源角度（0-360度）
    var elevation: CGFloat = 30.0 // 光源仰角（0-90度）
    var intensity: CGFloat = 0.3 // 光源强度（0-1）
    var softness: CGFloat = 0.5 // 光线柔和度（0-1）
    var colorTemperature: CGFloat = 5500 // 光源色温（2000-10000K）
    var tint: CGFloat = 0.0 // 光源色调偏移（-1到1）
}

// MARK: - 色温配置
struct ColorTemperatureConfig {
    var temperature: CGFloat = 5500 // 色温值（2000-10000K）
    var tint: CGFloat = 0.0 // 色调偏移（-1到1，负值偏绿，正值偏品红）
    var intensity: CGFloat = 0.5 // 色温影响强度（0-1）
}

struct LightConfiguration {
    var type: LightType = .directional
    var color: Color = .white
    var intensity: Float = 1.0
    var position: SIMD3<Float> = SIMD3<Float>(0, 1, 1)
    var direction: SIMD3<Float> = SIMD3<Float>(0, -1, -1)
    var spotInnerAngle: Float = 30.0
    var spotOuterAngle: Float = 45.0
}

// MARK: - 主视图
struct LightEnhanceView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userState: UserState
    
    @State private var selectedImage: UIImage?
    @State private var originalImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var isProcessing = false
    @State private var showImagePicker = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var beforeAfterPosition: CGFloat = 0.5
    @State private var segmentationMask: VNPixelBufferObservation?
    @State private var isInitialProcessing = false
    @State private var personCenterPoint: CGPoint = CGPoint(x: 0.5, y: 0.5) // 人物中心点（相对坐标）
    
    // 优化的补光配置（基于HumanLightEnhancement）
    @State private var featherRadius: CGFloat = 25.0
    
    // 新增：方向性光源配置
    @State private var directionalLight = DirectionalLightConfig()
    
    // 新增：色温配置
    @State private var colorTemperature = ColorTemperatureConfig()
    
    // UI控制
    @State private var selectedControlTab: ControlTab = .lighting
    
    enum ControlTab: String, CaseIterable, Identifiable {
        case lighting = "方向光源"
        case colorTemp = "色温控制"
        case advanced = "高级设置"
        var id: Self { self }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.white.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    headerView
                    
                    if selectedImage == nil {
                        emptyStateView
                    } else {
                        ScrollView(.vertical, showsIndicators: false) {
                            VStack(spacing: 20) {
                                imageDisplayView
                                    .padding(.horizontal, 20)
                                
                                controlPanelView
                                    .padding(.bottom, 100) // 给底部留出空间
                            }
                        }
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
        .onChange(of: selectedImage) { _, newImage in
            if let image = newImage {
                originalImage = image
                performHumanSegmentation()
            }
        }
    }
    
    // MARK: - 子视图
    private var headerView: some View {
        HStack {
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "arrow.left")
                    .font(.system(size: 18))
                    .foregroundColor(.black)
            }
            
            Spacer()
            
            Text("智能补光")
                .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                .foregroundColor(.black)
            
            Spacer()
            
            Button(action: {
                showImagePicker = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "photo")
                        .font(.system(size: 16))
                    Text("上传")
                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue)
                .cornerRadius(8)
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .padding(.bottom, 20)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "photo.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.gray.opacity(0.5))
            
            Text("选择一张人像照片开始补光")
                .font(Font.custom("PingFang SC", size: 18))
                .foregroundColor(.gray)
            
            Text("AI将自动识别人体并进行智能补光")
                .font(Font.custom("PingFang SC", size: 14))
                .foregroundColor(.gray.opacity(0.7))
            
            Button(action: {
                showImagePicker = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "person.fill")
                    Text("选择人像照片")
                        .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [Color.blue, Color.blue.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(10)
            }
            
            Spacer()
        }
    }
    
    private var imageDisplayView: some View {
        ZStack {
            Color.black.opacity(0.05)
                .cornerRadius(8)
            
            if let originalImg = originalImage {
                if let processed = processedImage {
                    BeforeAfterSlide(beforeImage: originalImg, afterImage: processed, sliderPosition: $beforeAfterPosition)
                        .cornerRadius(8)
                } else {
                    Image(uiImage: originalImg)
                        .resizable()
                        .scaledToFit()
                        .cornerRadius(8)
                }
            }
            
            if isProcessing || isInitialProcessing {
                VStack {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text(isInitialProcessing ? "AI分析中..." : "渲染中...")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                        .padding(.top, 10)
                }
            }
        }
        .frame(height: 400) // 固定高度，让图片显示更大
        .clipped()
    }
    
    private var controlPanelView: some View {
        VStack(spacing: 15) {
            // 控制标签页
            Picker("控制面板", selection: $selectedControlTab) {
                ForEach(ControlTab.allCases) { tab in
                    Text(tab.rawValue)
                }
            }
            .pickerStyle(.segmented)
            .padding(.horizontal, 20)
            
            // 控制内容
            ZStack {
                lightingControlsView
                    .opacity(selectedControlTab == .lighting ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .lighting)
                
                colorTemperatureControlsView
                    .opacity(selectedControlTab == .colorTemp ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .colorTemp)
                
                advancedControlsView
                    .opacity(selectedControlTab == .advanced ? 1 : 0)
                    .allowsHitTesting(selectedControlTab == .advanced)
            }
            .disabled(isProcessing)
            .opacity(isProcessing ? 0.5 : 1.0)
            
            // 保存按钮
            if processedImage != nil {
                Button(action: {
                    saveProcessedImage()
                }) {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                        Text("保存智能补光照片")
                            .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.green)
                    .cornerRadius(10)
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(15)
        .animation(.easeInOut(duration: 0.2), value: isProcessing)
    }

    
    // MARK: - 方向光源控制视图
    private var lightingControlsView: some View {
        VStack(spacing: 15) {
            VStack(spacing: 10) {
                Text("方向性光源")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                
                Text("模拟真实的方向性光照效果")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }
            
            EnhancedSlider(title: "光源角度", value: $directionalLight.angle, range: 0...360, step: 1, displayMultiplier: 1, unit: "°") {
                applyEnhancedLighting()
            }
            
            EnhancedSlider(title: "光源仰角", value: $directionalLight.elevation, range: 0...90, step: 1, displayMultiplier: 1, unit: "°") {
                applyEnhancedLighting()
            }
            
            EnhancedSlider(title: "光源强度", value: $directionalLight.intensity, range: 0...1, displayMultiplier: 100, unit: "%") {
                applyEnhancedLighting()
            }
            
            EnhancedSlider(title: "光线柔和度", value: $directionalLight.softness, range: 0...1, displayMultiplier: 100, unit: "%") {
                applyEnhancedLighting()
            }
            
            EnhancedSlider(title: "光源色温", value: $directionalLight.colorTemperature, range: 2000...10000, step: 100, displayMultiplier: 1, unit: "K") {
                applyEnhancedLighting()
            }
            
            EnhancedSlider(title: "光源色调", value: $directionalLight.tint, range: -1...1, displayMultiplier: 100, unit: "") {
                applyEnhancedLighting()
            }
            
            // 光源方向可视化指示器
            VStack(spacing: 10) {
                Text("光源方向预览")
                    .font(.system(size: 14))
                    .foregroundColor(.black)
                
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 2)
                        .frame(width: 100, height: 100)
                    
                    // 光源方向指示器
                    let angleRad = directionalLight.angle * .pi / 180.0
                    let arrowX = cos(angleRad - .pi/2) * 35
                    let arrowY = sin(angleRad - .pi/2) * 35
                    
                    Circle()
                        .fill(Color.yellow)
                        .frame(width: 12, height: 12)
                        .offset(x: arrowX, y: arrowY)
                    
                    // 中心点
                    Circle()
                        .fill(Color.blue)
                        .frame(width: 8, height: 8)
                    
                    // 方向箭头
                    Image(systemName: "arrow.up")
                        .font(.system(size: 16))
                        .foregroundColor(.yellow)
                        .rotationEffect(.degrees(directionalLight.angle))
                        .offset(x: arrowX, y: arrowY)
                }
                
                VStack(spacing: 2) {
                    Text("蓝点=人物中心，黄点=光源位置")
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                    Text("0°=上方照射，90°=右侧照射，180°=下方照射，270°=左侧照射")
                        .font(.system(size: 10))
                        .foregroundColor(.gray.opacity(0.8))
                }
            }
            .padding(.horizontal, 20)
            
            // 光源预设
            VStack(spacing: 10) {
                Text("光源预设")
                    .font(.system(size: 14))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                
                HStack(spacing: 10) {
                    Button("顶光") {
                        directionalLight.angle = 0
                        directionalLight.elevation = 60
                        directionalLight.intensity = 0.4
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue)
                    .cornerRadius(8)
                    
                    Button("侧光") {
                        directionalLight.angle = 90
                        directionalLight.elevation = 30
                        directionalLight.intensity = 0.3
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.orange)
                    .cornerRadius(8)
                    
                    Button("暖侧光") {
                        directionalLight.angle = 45
                        directionalLight.elevation = 25
                        directionalLight.colorTemperature = 3200
                        directionalLight.intensity = 0.35
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 6)
                    .background(Color.red)
                    .cornerRadius(8)
                    
                    Button("重置") {
                        directionalLight = DirectionalLightConfig()
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.black)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
                }
                .padding(.horizontal, 20)
            }
        }
    }
    
    // MARK: - 色温控制视图
    private var colorTemperatureControlsView: some View {
        VStack(spacing: 15) {
            VStack(spacing: 10) {
                Text("色温控制")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                
                Text("调节图像的色彩温度和色调平衡")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }
            
            EnhancedSlider(title: "色温", value: $colorTemperature.temperature, range: 2000...10000, step: 100, displayMultiplier: 1, unit: "K") {
                applyEnhancedLighting()
            }
            
            EnhancedSlider(title: "色调偏移", value: $colorTemperature.tint, range: -1...1, displayMultiplier: 100, unit: "") {
                applyEnhancedLighting()
            }
            
            EnhancedSlider(title: "强度", value: $colorTemperature.intensity, range: 0...1, displayMultiplier: 100, unit: "%") {
                applyEnhancedLighting()
            }
            
            HStack(spacing: 20) {
                VStack(spacing: 5) {
                    Text("暖色调")
                        .font(.system(size: 12))
                        .foregroundColor(.orange)
                    Text("2000K-4000K")
                        .font(.system(size: 10))
                        .foregroundColor(.gray)
                }
                
                VStack(spacing: 5) {
                    Text("自然光")
                        .font(.system(size: 12))
                        .foregroundColor(.black)
                    Text("5000K-6500K")
                        .font(.system(size: 10))
                        .foregroundColor(.gray)
                }
                
                VStack(spacing: 5) {
                    Text("冷色调")
                        .font(.system(size: 12))
                        .foregroundColor(.blue)
                    Text("7000K-10000K")
                        .font(.system(size: 10))
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal, 20)
            
            // 色温快速预设
            VStack(spacing: 10) {
                Text("快速预设")
                    .font(.system(size: 14))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                
                HStack(spacing: 10) {
                    Button("日光") {
                        colorTemperature.temperature = 5500
                        colorTemperature.tint = 0
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue)
                    .cornerRadius(8)
                    
                    Button("暖光") {
                        colorTemperature.temperature = 3200
                        colorTemperature.tint = 0.1
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.orange)
                    .cornerRadius(8)
                    
                    Button("冷光") {
                        colorTemperature.temperature = 7500
                        colorTemperature.tint = -0.1
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.cyan)
                    .cornerRadius(8)
                    
                    Button("重置") {
                        colorTemperature = ColorTemperatureConfig()
                        applyEnhancedLighting()
                    }
                    .foregroundColor(.black)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
                }
                .padding(.horizontal, 20)
            }
        }
    }
    
    private var advancedControlsView: some View {
        VStack(spacing: 15) {
            VStack(spacing: 10) {
                Text("高级设置")
                    .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                
                Text("精细调节补光效果的参数")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
            }
            
            EnhancedSlider(title: "羽化半径", value: $featherRadius, range: 0...100, step: 1, displayMultiplier: 1, unit: "px") {
                applyEnhancedLighting()
            }
            

            
            Text("羽化半径控制人物边缘与背景的融合程度，数值越大融合越柔和")
                .font(.system(size: 12))
                .foregroundColor(.gray)
                .padding(.horizontal, 20)
        }
    }
    
    // MARK: - 逻辑处理
    
    private func performHumanSegmentation() {
        guard let image = originalImage else { return }
        
        isInitialProcessing = true
        
        DispatchQueue.global(qos: .userInitiated).async {
            let segmentationRequest = VNGeneratePersonSegmentationRequest { request, error in
                if let error = error {
                    DispatchQueue.main.async {
                        isInitialProcessing = false
                        alertMessage = "人体分割错误: \(error.localizedDescription)"
                        showAlert = true
                    }
                    return
                }
                
                guard let observations = request.results as? [VNPixelBufferObservation],
                      let observation = observations.first else {
                    DispatchQueue.main.async {
                        isInitialProcessing = false
                        alertMessage = "未检测到人体，请选择包含人物的照片"
                        showAlert = true
                    }
                    return
                }
                
                DispatchQueue.main.async {
                    self.segmentationMask = observation
                    // 计算人物中心点
                    self.personCenterPoint = self.calculatePersonCenter(from: observation)
                    isInitialProcessing = false
                    applyEnhancedLighting()
                }
            }
            
            segmentationRequest.qualityLevel = .balanced
            
            guard let cgImage = image.cgImage else {
                DispatchQueue.main.async {
                    isInitialProcessing = false
                    alertMessage = "无法获取图像数据"
                    showAlert = true
                }
                return
            }
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([segmentationRequest])
            } catch {
                DispatchQueue.main.async {
                    isInitialProcessing = false
                    alertMessage = "处理图像时出错: \(error.localizedDescription)"
                    showAlert = true
                }
            }
        }
    }
    
    private func applyEnhancedLighting() {
        guard let originalImg = originalImage,
              let maskObservation = segmentationMask else { return }
        
        if isProcessing { return }
        
        isProcessing = true
        
        DispatchQueue.global(qos: .userInitiated).async {
            // 使用优化的补光算法
            let enhancedImage = OptimizedLightEnhancer.enhanceImage(
                image: originalImg,
                segmentation: maskObservation,
                featherRadius: featherRadius,
                directionalLight: directionalLight,
                colorTemperature: colorTemperature,
                personCenter: personCenterPoint
            )
            
            DispatchQueue.main.async {
                self.processedImage = enhancedImage
                self.isProcessing = false
            }
        }
    }
    
    private func calculatePersonCenter(from segmentation: VNPixelBufferObservation) -> CGPoint {
        // 从人体分割遮罩计算人物的中心点
        let pixelBuffer = segmentation.pixelBuffer
        CVPixelBufferLockBaseAddress(pixelBuffer, .readOnly)
        defer { CVPixelBufferUnlockBaseAddress(pixelBuffer, .readOnly) }
        
        let width = CVPixelBufferGetWidth(pixelBuffer)
        let height = CVPixelBufferGetHeight(pixelBuffer)
        let bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer)
        
        guard let baseAddress = CVPixelBufferGetBaseAddress(pixelBuffer) else {
            return CGPoint(x: 0.5, y: 0.5) // 默认中心点
        }
        
        var totalX: Double = 0
        var totalY: Double = 0
        var pixelCount: Double = 0
        
        // 遍历像素计算重心
        for y in 0..<height {
            let rowData = baseAddress.advanced(by: y * bytesPerRow)
            for x in 0..<width {
                let pixelValue = rowData.load(fromByteOffset: x, as: UInt8.self)
                let confidence = Double(pixelValue) / 255.0
                
                if confidence > 0.5 { // 只考虑置信度高的像素
                    totalX += Double(x) * confidence
                    totalY += Double(y) * confidence
                    pixelCount += confidence
                }
            }
        }
        
        if pixelCount > 0 {
            let centerX = totalX / pixelCount / Double(width)
            let centerY = totalY / pixelCount / Double(height)
            return CGPoint(x: centerX, y: centerY)
        }
        
        return CGPoint(x: 0.5, y: 0.5) // 默认中心点
    }
    

    
    private func saveProcessedImage() {
        guard let image = processedImage else { return }
        
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        alertMessage = "智能补光照片已保存到相册"
        showAlert = true
    }
}

// MARK: - 优化的补光增强处理类
class OptimizedLightEnhancer {
    private static var metalDevice: MTLDevice?
    
    static func enhanceImage(
        image: UIImage,
        segmentation: VNPixelBufferObservation,
        featherRadius: CGFloat,
        directionalLight: DirectionalLightConfig,
        colorTemperature: ColorTemperatureConfig,
        personCenter: CGPoint
    ) -> UIImage {
        guard let cgImage = image.cgImage else {
            return image
        }
        
        let ciImage = CIImage(cgImage: cgImage)
        let context = CIContext(options: [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .outputColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])
        
        // 1. 使用原始图像作为基础
        let baseImage = ciImage
        
        // 2. 创建并处理人体遮罩
        var maskCIImage = CIImage(cvPixelBuffer: segmentation.pixelBuffer)
        
        // 3. 缩放遮罩到与原图相同大小
        let imageSize = baseImage.extent.size
        let maskSize = maskCIImage.extent.size
        let scaleX = imageSize.width / maskSize.width
        let scaleY = imageSize.height / maskSize.height
        let scaleTransform = CGAffineTransform(scaleX: scaleX, y: scaleY)
        maskCIImage = maskCIImage.transformed(by: scaleTransform)
        
        // 4. 优化的羽化处理
        let featheredMask = applyOptimizedFeathering(to: maskCIImage, radius: featherRadius)
        
        // 5. 应用方向性光源效果
        let lightEnhancedImage = applyDirectionalLighting(
            to: baseImage,
            mask: featheredMask,
            lightConfig: directionalLight,
            personCenter: personCenter,
            imageSize: imageSize
        )
        
        // 6. 应用色温调整
        let colorTempAdjustedImage = applyColorTemperature(
            to: lightEnhancedImage,
            config: colorTemperature
        )
        
        // 7. 最终处理和输出
        guard let outputCGImage = context.createCGImage(colorTempAdjustedImage, from: colorTempAdjustedImage.extent) else {
            return image
        }
        
        return UIImage(cgImage: outputCGImage, scale: image.scale, orientation: image.imageOrientation)
    }
    

    
    // 优化的羽化处理
    private static func applyOptimizedFeathering(to mask: CIImage, radius: CGFloat) -> CIImage {
        // 使用双重模糊来获得更自然的羽化效果
        let blur1 = mask.applyingFilter("CIGaussianBlur", parameters: [
            kCIInputRadiusKey: radius * 0.5
        ])
        
        let blur2 = blur1.applyingFilter("CIGaussianBlur", parameters: [
            kCIInputRadiusKey: radius * 0.3
        ])
        
        // 混合两次模糊的结果
        return blur2.applyingFilter("CIAdditionCompositing", parameters: [
            kCIInputBackgroundImageKey: blur1
        ]).cropped(to: mask.extent)
    }
    

    
    // MARK: - 方向性光源处理
    private static func applyDirectionalLighting(
        to image: CIImage,
        mask: CIImage,
        lightConfig: DirectionalLightConfig,
        personCenter: CGPoint,
        imageSize: CGSize
    ) -> CIImage {
        guard lightConfig.intensity > 0 else { return image }
        
        // 创建方向性光照遮罩，只影响人体区域
        let lightMask = createDirectionalLightMask(
            size: imageSize,
            lightConfig: lightConfig,
            personCenter: personCenter,
            humanMask: mask
        )
        
        // 1. 先应用光源色温
        var workingImage = image
        if lightConfig.colorTemperature != 5500 || lightConfig.tint != 0 {
            workingImage = applyLightColorTemperature(to: image, lightConfig: lightConfig, mask: lightMask)
        }
        
        // 2. 创建亮度增强图像
        guard let brightnessFilter = CIFilter(name: "CIColorControls") else {
            return workingImage
        }
        
        brightnessFilter.setValue(workingImage, forKey: kCIInputImageKey)
        brightnessFilter.setValue(NSNumber(value: 1.0 + lightConfig.intensity * 0.5), forKey: kCIInputBrightnessKey)
        brightnessFilter.setValue(NSNumber(value: 1.0 + lightConfig.intensity * 0.3), forKey: kCIInputContrastKey)
        
        guard let brightenedImage = brightnessFilter.outputImage else { return workingImage }
        
        // 使用遮罩混合原图和增强图像
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            return image
        }
        
        blendFilter.setValue(brightenedImage, forKey: kCIInputImageKey)
        blendFilter.setValue(image, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(lightMask, forKey: kCIInputMaskImageKey)
        
        return blendFilter.outputImage ?? image
    }
    
    // 创建方向性光照遮罩
    private static func createDirectionalLightMask(
        size: CGSize,
        lightConfig: DirectionalLightConfig,
        personCenter: CGPoint,
        humanMask: CIImage
    ) -> CIImage {
        // 计算光源方向向量（修正坐标系）
        let angleRad = (lightConfig.angle - 90) * .pi / 180.0  // 修正角度，0度为上方
        let elevationFactor = lightConfig.elevation / 90.0
        
        // 计算光源位置（相对于人物中心）
        let lightDistance = max(size.width, size.height) * 0.8
        let lightX = personCenter.x * size.width + cos(angleRad) * lightDistance * elevationFactor
        let lightY = personCenter.y * size.height + sin(angleRad) * lightDistance * elevationFactor
        
        // 创建径向渐变作为光照基础
        guard let radialGradient = CIFilter(name: "CIRadialGradient") else {
            return humanMask
        }
        
        radialGradient.setValue(CIVector(x: lightX, y: lightY), forKey: "inputCenter")
        radialGradient.setValue(NSNumber(value: 0), forKey: "inputRadius0")
        radialGradient.setValue(NSNumber(value: lightDistance * (0.5 + lightConfig.softness)), forKey: "inputRadius1")
        radialGradient.setValue(CIColor(red: lightConfig.intensity, green: lightConfig.intensity, blue: lightConfig.intensity, alpha: 1.0), forKey: "inputColor0")
        radialGradient.setValue(CIColor(red: 0, green: 0, blue: 0, alpha: 1.0), forKey: "inputColor1")
        
        guard let gradientImage = radialGradient.outputImage else {
            return humanMask
        }
        
        let croppedGradient = gradientImage.cropped(to: CGRect(origin: .zero, size: size))
        
        // 将光照渐变与人体遮罩相乘，确保只影响人体区域
        guard let multiplyFilter = CIFilter(name: "CIMultiplyCompositing") else {
            return humanMask
        }
        
        multiplyFilter.setValue(croppedGradient, forKey: kCIInputImageKey)
        multiplyFilter.setValue(humanMask, forKey: kCIInputBackgroundImageKey)
        
        return multiplyFilter.outputImage ?? humanMask
    }
    
    // 应用光源色温（只影响光照区域）
    private static func applyLightColorTemperature(
        to image: CIImage,
        lightConfig: DirectionalLightConfig,
        mask: CIImage
    ) -> CIImage {
        // 创建色温调整
        guard let tempFilter = CIFilter(name: "CITemperatureAndTint") else {
            return image
        }
        
        tempFilter.setValue(image, forKey: kCIInputImageKey)
        
        // 将色温值转换为CIFilter期望的范围（与全局色温保持一致）
        let tempValue = (lightConfig.colorTemperature - 6500) / 3500.0  // 归一化到-1到1范围
        let tintValue = lightConfig.tint
        let tempVector = CIVector(x: tempValue, y: tintValue)
        tempFilter.setValue(tempVector, forKey: "inputNeutral")
        tempFilter.setValue(CIVector(x: 0, y: 0), forKey: "inputTargetNeutral")
        
        guard let tempAdjustedImage = tempFilter.outputImage else { return image }
        
        // 使用光照遮罩混合
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            return image
        }
        
        blendFilter.setValue(tempAdjustedImage, forKey: kCIInputImageKey)
        blendFilter.setValue(image, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(mask, forKey: kCIInputMaskImageKey)
        
        return blendFilter.outputImage ?? image
    }
    
    // MARK: - 色温处理
    private static func applyColorTemperature(
        to image: CIImage,
        config: ColorTemperatureConfig
    ) -> CIImage {
        guard config.intensity > 0 else { return image }
        
        // 1. 色温调整
        guard let tempFilter = CIFilter(name: "CITemperatureAndTint") else {
            return image
        }
        tempFilter.setValue(image, forKey: kCIInputImageKey)
        
        // 使用更简单有效的色温调整方式
        // CITemperatureAndTint的参数范围：温度-1到1，色调-1到1
        let tempValue = (config.temperature - 6500) / 3500.0  // 归一化到-1到1范围
        let tintValue = config.tint
        
        let tempVector = CIVector(x: tempValue, y: tintValue)
        tempFilter.setValue(tempVector, forKey: "inputNeutral")
        tempFilter.setValue(CIVector(x: 0, y: 0), forKey: "inputTargetNeutral")
        
        guard let tempAdjustedImage = tempFilter.outputImage else { return image }
        
        // 2. 强度混合 - 使用更简单的混合方式
        if config.intensity >= 1.0 {
            return tempAdjustedImage
        }
        
        // 创建权重遮罩来控制混合强度
        let weightColor = CIColor(red: config.intensity, green: config.intensity, blue: config.intensity, alpha: 1.0)
        let weightImage = CIImage(color: weightColor).cropped(to: image.extent)
        
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            return tempAdjustedImage
        }
        
        blendFilter.setValue(tempAdjustedImage, forKey: kCIInputImageKey)
        blendFilter.setValue(image, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(weightImage, forKey: kCIInputMaskImageKey)
        
        return blendFilter.outputImage ?? image
    }

}

// MARK: - 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// MARK: - 增强的滑块控件
struct EnhancedSlider: View {
    let title: String
    @Binding var value: CGFloat
    let range: ClosedRange<CGFloat>
    var step: CGFloat = 0.01
    var displayMultiplier: CGFloat = 1.0
    var unit: String = ""
    let onEditingChanged: () -> Void

    var body: some View {
        HStack {
            Text(title)
                .font(Font.custom("PingFang SC", size: 16))
                .foregroundColor(.black)
                .frame(width: 80, alignment: .leading)
            
            GeometryReader { geo in
                let width = geo.size.width
                let percentage = (value - range.lowerBound) / (range.upperBound - range.lowerBound)
                let knobPosition = width * percentage
                
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(LinearGradient(
                            colors: [Color.orange, Color.yellow],
                            startPoint: .leading,
                            endPoint: .trailing
                        ))
                        .frame(width: knobPosition, height: 4)
                        .cornerRadius(2)
                    
                    Image(systemName: "circle.fill")
                        .resizable()
                        .frame(width: 20, height: 20)
                        .foregroundColor(.white)
                        .shadow(radius: 3)
                        .overlay(Circle().stroke(Color.orange.opacity(0.3), lineWidth: 1))
                        .position(x: knobPosition, y: geo.size.height / 2)
                        .gesture(
                            DragGesture()
                                .onChanged { gestureValue in
                                    let newPercentage = max(0, min(1, gestureValue.location.x / width))
                                    let newValue = range.lowerBound + newPercentage * (range.upperBound - range.lowerBound)
                                    self.value = newValue
                                }
                                .onEnded { _ in
                                    onEditingChanged()
                                }
                        )
                }
            }
            .frame(height: 30)
            
            Text("\(Int(value * displayMultiplier))\(unit)")
                .font(.system(size: 14))
                .foregroundColor(.gray)
                .frame(width: 50, alignment: .trailing)
        }
        .padding(.horizontal, 20)
    }
}



// MARK: - 前后对比滑块控件
struct BeforeAfterSlide: View {
    let beforeImage: UIImage
    let afterImage: UIImage
    @Binding var sliderPosition: CGFloat
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 处理后的图像（底层）
                Image(uiImage: afterImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: geometry.size.width, height: geometry.size.height)
                
                // 原图（上层，被裁剪）
                Image(uiImage: beforeImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .clipShape(
                        Rectangle()
                            .path(in: CGRect(x: 0, y: 0, width: geometry.size.width * sliderPosition, height: geometry.size.height))
                    )
                
                // 分割线
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 2, height: geometry.size.height)
                    .position(x: geometry.size.width * sliderPosition, y: geometry.size.height / 2)
                    .shadow(color: Color.black.opacity(0.5), radius: 2, x: 0, y: 0)
                
                // 滑块控制柄
                Circle()
                    .fill(Color.white)
                    .frame(width: 30, height: 30)
                    .shadow(color: Color.black.opacity(0.5), radius: 2, x: 0, y: 0)
                    .overlay(
                        HStack(spacing: 3) {
                            Image(systemName: "arrow.left")
                                .font(.system(size: 8))
                            Image(systemName: "arrow.right")
                                .font(.system(size: 8))
                        }
                        .foregroundColor(.black)
                    )
                    .position(x: geometry.size.width * sliderPosition, y: 30)
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                let newPosition = value.location.x / geometry.size.width
                                sliderPosition = min(1, max(0, newPosition))
                            }
                    )
            }
        }
    }
}





 
